'use client'

import { useI18n } from '@/hooks/useI18n'

/**
 * Test component to verify FOUC fix is working
 * This component displays various translated texts that should appear
 * immediately in the correct language without flashing English first
 */
export function LanguageTestComponent() {
  const { t, language, setLanguage, isLoading } = useI18n()

  if (isLoading) {
    return (
      <div className="p-4 bg-dark-800 rounded-lg">
        <div className="text-dark-300">Loading translations...</div>
      </div>
    )
  }

  return (
    <div className="p-6 bg-dark-800 rounded-lg space-y-4">
      <h2 className="text-xl font-bold text-white mb-4">
        Language Test Component
      </h2>
      
      <div className="space-y-2">
        <p><strong>Current Language:</strong> {language}</p>
        <p><strong>Common Loading:</strong> {t('common.loading')}</p>
        <p><strong>Common Save:</strong> {t('common.save')}</p>
        <p><strong>Common Cancel:</strong> {t('common.cancel')}</p>
        <p><strong>Navigation Create Queue:</strong> {t('navigation.createQueue')}</p>
        <p><strong>Settings Title:</strong> {t('settings.title')}</p>
        <p><strong>App Title:</strong> {t('app.title')}</p>
      </div>

      <div className="flex gap-2 mt-4">
        <button
          onClick={() => setLanguage('en')}
          className={`px-3 py-1 rounded text-sm ${
            language === 'en' 
              ? 'bg-primary-600 text-white' 
              : 'bg-dark-700 text-dark-300 hover:bg-dark-600'
          }`}
        >
          English
        </button>
        <button
          onClick={() => setLanguage('es')}
          className={`px-3 py-1 rounded text-sm ${
            language === 'es' 
              ? 'bg-primary-600 text-white' 
              : 'bg-dark-700 text-dark-300 hover:bg-dark-600'
          }`}
        >
          Español
        </button>
      </div>

      <div className="text-sm text-dark-400 mt-4">
        <p>
          <strong>Test Instructions:</strong> Refresh the page and watch for any flash of English text 
          before Spanish appears. If the FOUC fix is working, you should see Spanish text immediately 
          without any English flash.
        </p>
      </div>
    </div>
  )
}
