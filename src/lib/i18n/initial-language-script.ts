/**
 * <PERSON><PERSON><PERSON> to set initial language attribute on HTML element
 * This runs before React hydration to prevent FOUC
 */
export const initialLanguageScript = `
(function() {
  try {
    // Get stored language from localStorage
    var stored = localStorage.getItem('youtube-looper-language');
    var language = 'en'; // default
    
    if (stored && ['en', 'es', 'to'].includes(stored)) {
      language = stored;
    } else {
      // Try to detect from browser language
      var browserLang = navigator.language.split('-')[0];
      if (['en', 'es', 'to'].includes(browserLang)) {
        language = browserLang;
        // Store the detected language
        localStorage.setItem('youtube-looper-language', language);
      }
    }
    
    // Set the HTML lang attribute immediately
    document.documentElement.lang = language;
  } catch (error) {
    console.warn('Failed to set initial language:', error);
    document.documentElement.lang = 'en';
  }
})();
`
